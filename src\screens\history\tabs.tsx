'use client';
import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
} from 'react-native';
import LendHistoryScreen from './lendHistory';
import LoanHistoryScreen from './loanHistory';
import {colors} from '../../theme/colors';

type TabType = 'Lend' | 'Loan';

export default function CustomTabs() {
  const [activeTab, setActiveTab] = useState<TabType>('Lend');
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const toggleFilter = (filter: string) => {
    if (selectedFilters.includes(filter)) {
      setSelectedFilters(selectedFilters.filter(f => f !== filter));
    } else {
      setSelectedFilters([...selectedFilters, filter]);
    }
  };

  return (
    <View style={styles.container}>
      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Lend' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Lend')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Lend' && styles.activeTabText,
            ]}>
            Lend History
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Loan' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Loan')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Loan' && styles.activeTabText,
            ]}>
            Loan History
          </Text>
        </TouchableOpacity>
      </View>

      {/* Divider with Filter */}
      <View style={styles.divider}>
        <Text>Active cases</Text>
        <TouchableOpacity onPress={() => setIsFilterVisible(true)}>
          <Text style={{color: colors.primary.main, fontWeight: '600'}}>
            Filter
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'Lend' ? <LendHistoryScreen /> : <LoanHistoryScreen />}
      </View>

      {/* Filter Modal */}
      <Modal
        visible={isFilterVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsFilterVisible(false)}>
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setIsFilterVisible(false)}
        />
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Filter Options</Text>

          {['Pending', 'Completed', 'Overdue'].map(option => (
            <TouchableOpacity
              key={option}
              style={styles.checkboxRow}
              onPress={() => toggleFilter(option)}>
              <View
                style={[
                  styles.checkbox,
                  selectedFilters.includes(option) && styles.checkboxSelected,
                ]}
              />
              <Text style={styles.checkboxLabel}>{option}</Text>
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            style={styles.applyButton}
            onPress={() => setIsFilterVisible(false)}>
            <Text style={styles.applyButtonText}>Apply</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    padding: 4,
    margin: 0,
    // marginTop: -32,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 32,
    backgroundColor: '#F3F4F6',
  },
  activeTabButton: {
    backgroundColor: colors.primary.main,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
  divider: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContent: {
    backgroundColor: '#fff',
    padding: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#ccc',
    marginRight: 10,
  },
  checkboxSelected: {
    backgroundColor: colors.primary.main,
    borderColor: colors.primary.main,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#333',
  },
  applyButton: {
    backgroundColor: colors.primary.main,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
});
