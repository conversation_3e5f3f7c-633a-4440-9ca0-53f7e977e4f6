import Svg, {Path, Circle} from 'react-native-svg';

export const HomeIcon = ({size = 24, color = 'black'}) => (
  <Svg width={size} height={(size * 18) / 15} viewBox="0 0 15 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.91667 3.16659C9.91667 1.87792 8.872 0.833252 7.58333 0.833252C6.29467 0.833252 5.25 1.87792 5.25 3.16659H9.91667Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M4.6135 9.63629C4.32061 9.3434 3.84573 9.3434 3.55284 9.63629C3.25995 9.92918 3.25995 10.4041 3.55284 10.6969L4.6135 9.63629ZM6.4165 12.5L5.88617 13.0303C6.17907 13.3232 6.65394 13.3232 6.94683 13.0303L6.4165 12.5Z"
      fill={color}
    />
  </Svg>
);

export const SearchIcon = ({size = 24, color = 'black'}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Circle cx="11" cy="11" r="7" stroke={color} strokeWidth={2} />
    <Path d="M20 20L16.65 16.65" stroke={color} strokeWidth={2} />
  </Svg>
);

export const SettingsIcon = ({size = 24, color = 'black'}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 8a4 4 0 100 8 4 4 0 000-8z" stroke={color} strokeWidth={2} />
    <Path
      d="M3 12h2M19 12h2M12 3v2M12 19v2M4.22 4.22l1.42 1.42
         M18.36 18.36l1.42 1.42M4.22 19.78l1.42-1.42
         M18.36 5.64l1.42-1.42"
      stroke={color}
      strokeWidth={2}
    />
  </Svg>
);

export const ReplayIcon = ({width = 16, height = 16, color = '#6AA894'}) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" fill="none">
    <Path
      d="M13.5689 6.205C13.6829 6.60322 14.0981 6.83363 14.4964 6.71964C14.8946 6.60565 15.125 6.19042 15.011 5.7922L13.5689 6.205ZM12.59 3.0486L13.1301 2.52824L13.1295 2.52764L12.59 3.0486ZM9.11096 1.1336L9.2625 0.399071L9.2611 0.398784L9.11096 1.1336ZM5.18996 1.5336L4.89453 0.844239L4.89379 0.844557L5.18996 1.5336ZM2.14396 4.1096L1.51365 3.70314L1.51364 3.70315L2.14396 4.1096ZM2.98796 12.9486L3.53029 12.4306L3.52996 12.4302L2.98796 12.9486ZM11.557 13.8196L11.984 14.4362L11.9841 14.4361L11.557 13.8196ZM14.7536 10.9554C14.907 10.5706 14.7195 10.1343 14.3347 9.98094C13.95 9.82753 13.5137 10.0151 13.3603 10.3998L14.7536 10.9554ZM13.5641 5.80996C13.4599 6.21085 13.7004 6.6203 14.1013 6.72449C14.5022 6.82868 14.9117 6.58815 15.0158 6.18725L13.5641 5.80996ZM15.7258 3.45525C15.83 3.05435 15.5895 2.6449 15.1886 2.54072C14.7877 2.43653 14.3783 2.67706 14.2741 3.07796L15.7258 3.45525ZM14.0902 6.72151C14.4894 6.83184 14.9025 6.59762 15.0129 6.19836C15.1232 5.79911 14.889 5.38602 14.4897 5.2757L14.0902 6.72151ZM11.8407 4.5437C11.4415 4.43337 11.0284 4.66759 10.918 5.06684C10.8077 5.46609 11.0419 5.87919 11.4412 5.98951L11.8407 4.5437ZM15.011 5.7922C14.6602 4.56683 14.0144 3.44613 13.1301 2.52824L12.0498 3.56897C12.764 4.31028 13.2856 5.21537 13.5689 6.205L15.011 5.7922ZM13.1295 2.52764C12.0842 1.44503 10.7364 0.703154 9.2625 0.399073L8.95941 1.86813C10.1375 2.11119 11.2148 2.7042 12.0504 3.56956L13.1295 2.52764ZM9.2611 0.398784C7.79418 0.099059 6.27071 0.254476 4.89453 0.844239L5.48538 2.22297C6.58071 1.75356 7.79327 1.62986 8.96082 1.86842L9.2611 0.398784ZM4.89379 0.844557C3.50796 1.44022 2.33115 2.43545 1.51365 3.70314L2.77426 4.51607C3.43013 3.49901 4.37429 2.70054 5.48612 2.22265L4.89379 0.844557ZM1.51364 3.70315C-0.471459 6.78162 -0.0858661 10.8198 2.44595 13.467L3.52996 12.4302C1.47778 10.2845 1.16523 7.01133 2.77427 4.51605L1.51364 3.70315ZM2.44562 13.4666C4.95364 16.0923 8.99893 16.5035 11.984 14.4362L11.13 13.203C8.75163 14.8501 5.52855 14.5225 3.53029 12.4306L2.44562 13.4666ZM11.9841 14.4361C13.2293 13.5732 14.1926 12.3625 14.7536 10.9554L13.3603 10.3998C12.9084 11.5332 12.1326 12.5082 11.1298 13.2032L11.9841 14.4361ZM15.0158 6.18725L15.7258 3.45525L14.2741 3.07796L13.5641 5.80996L15.0158 6.18725ZM14.4897 5.2757L11.8407 4.5437L11.4412 5.98951L14.0902 6.72151L14.4897 5.2757Z"
      fill={color}
    />
  </Svg>
);

export const AccountIcon = ({width = 28, height = 29, color = 'black'}) => (
  <Svg width={width} height={height} viewBox="0 0 28 29" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.2005 9.51674C18.2005 11.8363 16.3201 13.7166 14.0006 13.7166C11.6811 13.7166 9.80078 11.8363 9.80078 9.51674C9.80078 7.19723 11.6811 5.31689 14.0006 5.31689C15.1145 5.31689 16.1827 5.75938 16.9704 6.547C17.758 7.33462 18.2005 8.40287 18.2005 9.51674Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.5939 16.5166H10.4066C9.95471 16.5172 9.50613 16.5934 9.07942 16.742C4.1656 18.4373 8.84003 24.9163 14.0002 24.9163C19.1605 24.9163 23.8349 18.4373 18.9197 16.742C18.4934 16.5935 18.0453 16.5173 17.5939 16.5166Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

// Notification Icon
export const NotificationIcon = ({size = 28, color = 'black'}) => (
  <Svg width={size} height={size} viewBox="0 0 28 29" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.55439 15.1902C7.41904 11.4909 10.3024 8.37946 14.0014 8.2334C17.7003 8.37946 20.5837 11.4909 20.4484 15.1902C20.4484 16.7267 22.1179 18.2236 22.1669 19.7636C22.1669 19.7853 22.1669 19.8071 22.1669 19.8289C22.203 20.8156 21.4338 21.6453 20.4472 21.6839H16.7232C16.7267 22.4399 16.4417 23.1688 15.9264 23.7221C15.4309 24.2601 14.7328 24.5663 14.0014 24.5663C13.2699 24.5663 12.5719 24.2601 12.0764 23.7221C11.5611 23.1688 11.2761 22.4399 11.2796 21.6839H7.55439C6.56782 21.6453 5.79859 20.8156 5.83472 19.8289C5.83472 19.8071 5.83472 19.7853 5.83472 19.7636C5.88489 18.2282 7.55439 16.7279 7.55439 15.1902Z"
      stroke={color}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M11.2793 20.934C10.8651 20.934 10.5293 21.2698 10.5293 21.684C10.5293 22.0982 10.8651 22.434 11.2793 22.434V20.934ZM16.723 22.434C17.1372 22.434 17.473 22.0982 17.473 21.684C17.473 21.2698 17.1372 20.934 16.723 20.934V22.434ZM15.1678 6.65015C15.582 6.65015 15.9178 6.31436 15.9178 5.90015C15.9178 5.48593 15.582 5.15015 15.1678 5.15015V6.65015ZM12.8345 5.15015C12.4202 5.15015 12.0845 5.48593 12.0845 5.90015C12.0845 6.31436 12.4202 6.65015 12.8345 6.65015V5.15015ZM11.2793 22.434H16.723V20.934H11.2793V22.434ZM15.1678 5.15015H12.8345V6.65015H15.1678V5.15015Z"
      fill={color}
    />
  </Svg>
);
