'use client';
import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  ArrowLeft,
  CreditCard,
  Bank,
  GoogleLogo,
  Circle,
  CaretDown,
  CaretUp,
} from 'phosphor-react-native';
import {useNavigation} from '@react-navigation/native';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';
import Layout from '../../components/Layout';

type RootStackParamList = {
  ProceedToPay: undefined;
  PaymentOptions: undefined;
};

export default function PaymentOptionsScreen() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const [selectedOption, setSelectedOption] = useState<string>('Google Pay');
  const [expandedSections, setExpandedSections] = useState<string[]>(['UPI']);

  const toggleSection = (section: string) => {
    setExpandedSections(
      prev =>
        prev.includes(section)
          ? prev.filter(s => s !== section) // collapse
          : [...prev, section], // expand
    );
  };

  const options = [
    {
      section: 'UPI',
      description: 'Pay by any UPI app.',
      items: ['Google Pay', 'Paytm', 'Other UPI'],
    },
    {
      section: 'Card / Debit card',
      description: 'Visa, Mastercard, Rupay & more',
      items: ['Visa', 'Mastercard', 'Rupay'],
    },
    {
      section: 'Net banking',
      description: 'Pay through your favourite bank',
      items: ['SBI', 'HDFC', 'ICICI', 'Axis'],
    },
  ];

  return (
    <Layout
      title="Payment Options"
      showBack
      style={styles.container}
      // rightContent={renderRightContent()}
    >
      <View style={styles.container}>
        {/* Options */}
        <ScrollView contentContainerStyle={{padding: 16, paddingBottom: 80}}>
          {options.map((opt, index) => {
            const isExpanded = expandedSections.includes(opt.section);

            return (
              <View key={index} style={styles.card}>
                {/* Section title */}
                <TouchableOpacity
                  style={styles.rowBetween}
                  onPress={() => toggleSection(opt.section)}>
                  <View style={styles.row}>
                    {opt.section.includes('UPI') ? (
                      <GoogleLogo size={20} color="black" weight="regular" />
                    ) : opt.section.includes('Card') ? (
                      <CreditCard size={20} color="black" weight="regular" />
                    ) : (
                      <Bank size={20} color="black" weight="regular" />
                    )}
                    <View style={{marginLeft: 8}}>
                      <Text style={styles.sectionTitle}>{opt.section}</Text>
                      <Text style={styles.sectionDesc}>{opt.description}</Text>
                    </View>
                  </View>

                  {isExpanded ? (
                    <CaretUp size={20} color="black" />
                  ) : (
                    <CaretDown size={20} color="black" />
                  )}
                </TouchableOpacity>

                {/* Items inside expandable card */}
                {isExpanded &&
                  opt.items.map(item => (
                    <TouchableOpacity
                      key={item}
                      style={styles.itemRow}
                      onPress={() => setSelectedOption(item)}>
                      <View style={styles.row}>
                        <GoogleLogo
                          size={20}
                          color="black"
                          weight="regular"
                          style={{marginRight: 8}}
                        />
                        <Text style={styles.itemText}>{item}</Text>
                      </View>
                      {selectedOption === item ? (
                        <Circle size={20} weight="fill" color="#0d47a1" />
                      ) : (
                        <Circle size={20} weight="regular" color="#aaa" />
                      )}
                    </TouchableOpacity>
                  ))}
              </View>
            );
          })}
        </ScrollView>

        {/* Bottom Button */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.button}
            // onPress={() => navigation.navigate('ProceedToPay')}
          >
            <Text style={styles.buttonText}>Repay</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#fff'},
  header: {flexDirection: 'row', alignItems: 'center', padding: 16},
  headerTitle: {fontSize: 18, fontWeight: '600', marginLeft: 10},
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    shadowOffset: {width: 0, height: 2},
  },
  row: {flexDirection: 'row', alignItems: 'center'},
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {fontSize: 15, fontWeight: '600'},
  sectionDesc: {fontSize: 13, color: '#555'},
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingLeft: 28,
  },
  itemText: {fontSize: 14, color: '#333'},
  footer: {padding: 16, backgroundColor: '#fff'},
  button: {
    backgroundColor: '#002d72',
    paddingVertical: 14,
    borderRadius: 50,
    alignItems: 'center',
  },
  buttonText: {color: '#fff', fontSize: 16, fontWeight: '600'},
});
