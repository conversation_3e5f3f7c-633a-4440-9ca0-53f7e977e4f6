// screens/home.js
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Layout from '../../components/Layout';
import {CaretRight} from 'phosphor-react-native';
import RepaymentCarousel from './carousel';
import ActiveCard from '../../globalComponents/activeCard';
import {CustomHomeIcon, HomeIcon, ReplayIcon} from '../../components/Icons';

interface ActiveCase {
  name: string;
  amountTitle: string;
  amount: string;
  date: string;
  tag: string;
  image: string;
}

const data: ActiveCase[] = [
  {
    name: '<PERSON><PERSON><PERSON>',
    amountTitle: 'Amount to be repaid',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    amountTitle: '<PERSON>t Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
  {
    name: 'Saksham Jha',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '29 Dec, 2024',
    tag: 'Today',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    name: 'Rohit Sharma',
    amountTitle: 'Lent Amount',
    amount: '20,000',
    date: '2nd Jan, 2025',
    tag: '',
    image: 'https://randomuser.me/api/portraits/men/35.jpg',
  },
];

export default function HomeScreen() {
  const navigation = useNavigation();

  const handleNotificationPress = () => {
    navigation.navigate('Notifications' as never);
  };

  const handleProfilePress = () => {
    navigation.navigate('Account' as never);
  };

  const handleBorrowPress = () => {
    navigation.navigate('BorrowMoney' as never);
  };

  const handleRepayPress = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <Layout
      title="Home"
      showNotification
      showProfile
      onNotificationPress={handleNotificationPress}
      onProfilePress={handleProfilePress}
      style={styles.layoutContainer}>
      <ScrollView style={styles.scrollView}>
        {/* Repayment Carousel */}
        <View style={styles.repaymentCarousel}>
          <RepaymentCarousel />
        </View>

        {/* Quick Actions */}
        <View style={styles.mainContainer}>
          <Text style={styles.sectionTitle}>Actions</Text>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleBorrowPress}>
              <View style={styles.actionContent}>
                <CustomHomeIcon size={20} />
                <Text style={styles.actionText}>Borrow Money</Text>
              </View>
              <CaretRight size={20} color="#6B7280" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleRepayPress}>
              <View style={styles.actionContent}>
                <ReplayIcon width={20} height={20} />
                <Text style={styles.actionText}>Repay Money</Text>
              </View>
              <CaretRight size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Active Cases */}
        <View style={styles.casesContainer}>
          <Text style={styles.sectionTitle}>Active Cases</Text>
          {data.map((item, index) => (
            <View key={index} style={styles.cardWrapper}>
              <ActiveCard
                name={item.name}
                amountTitle={item.amountTitle}
                amount={item.amount}
                dueDate={item.date}
                profileImage={item.image}
                onRepay={() =>
                  navigation.navigate('ProceedToPay' as never, {item})
                }
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  repaymentCarousel: {
    marginVertical: 16,
  },

  casesContainer: {
    padding: 16,
  },

  cardWrapper: {
    marginBottom: 12,
  },

  // for button
  mainContainer: {
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#111827',
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12, // works in RN 0.71+ (else use marginBottom)
  },
  actionButton: {
    width: '48%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#6AA894',
    backgroundColor: '#FFFFFF',
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  actionText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
  },
});
