import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface RepaymentCardProps {
  title: string;
  text: string;
  onLater: () => void;
  onViewMore: () => void;
}

const RepaymentCard: React.FC<RepaymentCardProps> = ({
  title,
  text,
  onLater,
  onViewMore,
}) => {
  return (
    <LinearGradient
      colors={['#EDF7F4', '#E5EFFB']}
      //   start={{x: 0, y: 0}}
      //   end={{x: 1, y: 0}}
      style={styles.card}>
      <View style={styles.content}>
        <View>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{text}</Text>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity onPress={onLater}>
            <Text style={styles.laterText}>Later</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.viewMoreBtn} onPress={onViewMore}>
            <Text style={styles.viewMoreText}>View more</Text>
          </TouchableOpacity>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    padding: 18,
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between', // pushes footer to bottom
  },
  title: {
    fontSize: 20,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
    color: '#5D5D5D',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  laterText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#111827',
    textDecorationLine: 'underline',
  },
  viewMoreBtn: {
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    elevation: 1,
    color: '#003272',
  },
  viewMoreText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#003272',
  },
});

export default RepaymentCard;
