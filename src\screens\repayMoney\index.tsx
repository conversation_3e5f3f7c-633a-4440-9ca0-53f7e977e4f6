import React, {useState} from 'react';
import {View, Text, StyleSheet, TextInput, FlatList, Alert} from 'react-native';
import Layout from '../../components/Layout';
import {MagnifyingGlass} from 'phosphor-react-native';
import LoanCard from '../../globalComponents/LoanCard';

const RepayMoneyScreen = ({navigation}: any) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Your LoanDataArray
  const LoanDataArray = [
    {
      Date: '2nd Jan, 2025',
      lendData: [
        {
          id: '1',
          name: '<PERSON><PERSON><PERSON>',
          tag: 'Real Friends',
          amount: '₹20,000',
          dueDate: '2nd Jan, 2025',
          profileImage: 'https://i.pravatar.cc/150?img=3',
          onRepay: () => console.log('Repay pressed for Rohit Sharma'),
          gradientColors: ['#E0F7FA', '#B2EBF2'],
        },
        {
          id: '2',
          name: '<PERSON><PERSON><PERSON>',
          tag: 'College Buddies',
          amount: '₹15,500',
          dueDate: '12th Feb, 2025',
          profileImage: 'https://i.pravatar.cc/150?img=5',
          onRepay: () => console.log('Repay pressed for Virat Kohli'),
          gradientColors: ['#FCE4EC', '#F8BBD0'],
        },
        {
          id: '3',
          name: 'KL Rahul',
          tag: 'Close Circle',
          amount: '₹10,000',
          dueDate: '5th Mar, 2025',
          profileImage: 'https://i.pravatar.cc/150?img=7',
          onRepay: () => console.log('Repay pressed for KL Rahul'),
          gradientColors: ['#FFF3E0', '#FFE0B2'],
        },
      ],
    },
    {
      Date: '18th Apr, 2025',
      lendData: [
        {
          id: '4',
          name: 'Hardik Pandya',
          tag: 'Gym Bro',
          amount: '₹8,200',
          dueDate: '18th Apr, 2025',
          profileImage: 'https://i.pravatar.cc/150?img=9',
          onRepay: () => console.log('Repay pressed for Hardik Pandya'),
          gradientColors: ['#E8F5E9', '#C8E6C9'],
        },
        {
          id: '5',
          name: 'MS Dhoni',
          tag: 'Mentor',
          amount: '₹25,000',
          dueDate: '30th May, 2025',
          profileImage: 'https://i.pravatar.cc/150?img=11',
          onRepay: () => console.log('Repay pressed for MS Dhoni'),
          gradientColors: ['#EDE7F6', '#D1C4E9'],
        },
      ],
    },
  ];

  // Flatten lendData for FlatList
  const flattenedLoans = LoanDataArray.flatMap(section => section.lendData);

  const renderLoanItem = ({item}: any) => (
    <LoanCard
      key={item.id}
      name={item.name}
      tag={item.tag}
      amount={item.amount}
      dueDate={item.dueDate}
      profileImage={item.profileImage}
      onRepay={() => navigation.navigate('ProceedToPay' as never, {item})}
      gradientColors={item.gradientColors}
    />
  );

  return (
    <Layout title="Repay Money" showBack style={styles.layoutContainer}>
      <View style={styles.content}>
        {/* Search Input */}
        <View style={styles.searchContainer}>
          <MagnifyingGlass size={20} color="#9CA3AF" weight="bold" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by lender name"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Loans List */}
        <View style={styles.loansContainer}>
          <Text style={styles.sectionTitle}>Active Loans</Text>
          <FlatList
            data={flattenedLoans.filter(loan =>
              loan.name.toLowerCase().includes(searchQuery.toLowerCase()),
            )}
            renderItem={renderLoanItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  layoutContainer: {
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    marginLeft: 8,
    fontSize: 16,
    color: '#1F2937',
  },
  loansContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-SemiBold',
    color: '#1F2937',
    marginBottom: 12,
  },
});

export default RepayMoneyScreen;
