import React from 'react';
import {Text, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import TitleHeader from '../../header/titleHeader';
import CustomTabs from './tabs';
import Layout from '../../components/Layout';

const HistoryScreen = () => {
  return (
    <Layout title="History">
      <SafeAreaView style={styles.container}>
        <CustomTabs />
      </SafeAreaView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
});

export default HistoryScreen;
