import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import LenderModeScreen from './lenderMode';
import BorrowerModeScreen from './borrowerMode';
import {colors} from '../../theme/colors';

type TabType = 'Lender' | 'Borrower';

export default function CustomTabs() {
  const [activeTab, setActiveTab] = useState<TabType>('Lender');

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Lender' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Lender')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Lender' && styles.activeTabText,
            ]}>
            Lender Mode
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'Borrower' && styles.activeTabButton,
          ]}
          onPress={() => handleTabPress('Borrower')}>
          <Text
            style={[
              styles.tabText,
              activeTab === 'Borrower' && styles.activeTabText,
            ]}>
            Borrower Mode
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {activeTab === 'Lender' ? <LenderModeScreen /> : <BorrowerModeScreen />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 32,
    padding: 4,
    margin: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 32,
  },
  activeTabButton: {
    backgroundColor: colors.primary.main,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
  },
});
