const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  resolver: {
    ...defaultConfig.resolver,
    assetExts: defaultConfig.resolver.assetExts.filter(ext => ext !== 'svg'),
    sourceExts: [...defaultConfig.resolver.sourceExts, 'svg'],
    blockList: [
      /.*\/node_modules\/@aws-amplify\/react-native\/android\/build\/.*/,
      /.*\/node_modules\/@aws-amplify\/react-native\/ios\/build\/.*/,
      /.*\/android\/build\/.*/,
      /.*\/ios\/build\/.*/,
    ],
  },
  transformer: {
    ...defaultConfig.transformer,
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  watchFolders: [path.resolve(__dirname)],
};

module.exports = mergeConfig(defaultConfig, config);
