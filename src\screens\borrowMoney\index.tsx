// // src/screens/auth/OnboardingScreen.tsx
// import React, {useState} from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   TextInput,
//   TouchableOpacity,
//   ScrollView,
// } from 'react-native';
// import Layout from '../../components/Layout';
// import {CurrencyDollar, User, Calendar} from 'phosphor-react-native';

// const BorrowMoneyScreen = () => {
//   const [amount, setAmount] = useState('');
//   const [duration, setDuration] = useState('');
//   const [purpose, setPurpose] = useState('');

//   return (
//     <Layout
//       title="Borrow Money"
//       showBack
//       transparent
//       style={styles.layoutContainer}>
//       <ScrollView style={styles.scrollView}>
//         <View style={styles.content}>
//           {/* Amount Input Section */}
//           <View style={styles.section}>
//             <Text style={styles.sectionTitle}>How much do you need?</Text>
//             <View style={styles.inputContainer}>
//               <CurrencyDollar size={24} color="#4F46E5" weight="bold" />
//               <TextInput
//                 style={styles.input}
//                 placeholder="Enter amount"
//                 keyboardType="numeric"
//                 value={amount}
//                 onChangeText={setAmount}
//                 placeholderTextColor="#9CA3AF"
//               />
//             </View>
//           </View>

//           {/* Duration Input Section */}
//           <View style={styles.section}>
//             <Text style={styles.sectionTitle}>For how long?</Text>
//             <View style={styles.inputContainer}>
//               <Calendar size={24} color="#4F46E5" weight="bold" />
//               <TextInput
//                 style={styles.input}
//                 placeholder="Duration in days"
//                 keyboardType="numeric"
//                 value={duration}
//                 onChangeText={setDuration}
//                 placeholderTextColor="#9CA3AF"
//               />
//             </View>
//           </View>

//           {/* Purpose Input Section */}
//           <View style={styles.section}>
//             <Text style={styles.sectionTitle}>Purpose of borrowing</Text>
//             <View style={styles.inputContainer}>
//               <User size={24} color="#4F46E5" weight="bold" />
//               <TextInput
//                 style={styles.input}
//                 placeholder="Enter purpose"
//                 value={purpose}
//                 onChangeText={setPurpose}
//                 placeholderTextColor="#9CA3AF"
//                 multiline
//               />
//             </View>
//           </View>

//           {/* Terms and Conditions */}
//           <View style={styles.termsContainer}>
//             <Text style={styles.termsText}>
//               By proceeding, you agree to our terms and conditions for borrowing
//               money.
//             </Text>
//           </View>
//         </View>
//       </ScrollView>

//       {/* Bottom Button */}
//       <View style={styles.bottomContainer}>
//         <TouchableOpacity style={styles.submitButton}>
//           <Text style={styles.submitButtonText}>Continue</Text>
//         </TouchableOpacity>
//       </View>
//     </Layout>
//   );
// };

// const styles = StyleSheet.create({
//   layoutContainer: {
//     backgroundColor: '#FFFFFF',
//   },
//   scrollView: {
//     flex: 1,
//   },
//   content: {
//     padding: 20,
//   },
//   section: {
//     marginBottom: 24,
//   },
//   sectionTitle: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: '#1F2937',
//     marginBottom: 8,
//   },
//   inputContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: '#F3F4F6',
//     borderRadius: 12,
//     paddingHorizontal: 16,
//     paddingVertical: 12,
//   },
//   input: {
//     flex: 1,
//     marginLeft: 12,
//     fontSize: 16,
//     color: '#1F2937',
//   },
//   termsContainer: {
//     marginTop: 16,
//     padding: 16,
//     backgroundColor: '#F3F4F6',
//     borderRadius: 12,
//   },
//   termsText: {
//     fontSize: 14,
//     color: '#6B7280',
//     lineHeight: 20,
//   },
//   bottomContainer: {
//     padding: 20,
//     backgroundColor: '#FFFFFF',
//     borderTopWidth: 1,
//     borderTopColor: '#E5E7EB',
//   },
//   submitButton: {
//     backgroundColor: '#4F46E5',
//     paddingVertical: 16,
//     borderRadius: 12,
//     alignItems: 'center',
//   },
//   submitButtonText: {
//     color: '#FFFFFF',
//     fontSize: 16,
//     fontWeight: '600',
//   },
// });

// export default BorrowMoneyScreen;

// BorrowMoneyScreen.tsx
import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Switch,
  TextInput,
  Text,
  TouchableOpacity,
} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import DateTimePicker from '@react-native-community/datetimepicker';
import Layout from '../../components/Layout';

export default function BorrowMoneyScreen() {
  const [friendsTag, setFriendsTag] = useState(null);
  const [amount, setAmount] = useState('');
  const [repaymentDate, setRepaymentDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [interest, setInterest] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [validity, setValidity] = useState('');
  const [validityUnit, setValidityUnit] = useState('hours');
  const [customMessage, setCustomMessage] = useState('');

  const units = [
    {label: 'In hours', value: 'hours'},
    {label: 'In days', value: 'days'},
    {label: 'In weeks', value: 'weeks'},
  ];

  return (
    <Layout
      title="Borrow Money"
      showBack
      style={styles.container}
      // rightContent={renderRightContent()}
    >
      <ScrollView
        style={styles.container}
        contentContainerStyle={{padding: 20}}>
        {/* Friends/Tags */}
        <Dropdown
          style={styles.dropdown}
          data={[
            {label: 'All', value: 'All'},
            {label: 'Friends', value: 'Friends'},
            {label: 'Tags', value: 'Tags'},
          ]}
          labelField="label"
          valueField="value"
          placeholder="Select Friends/Tags*"
          value={friendsTag}
          onChange={item => setFriendsTag(item.value)}
        />

        {/* Amount & Repayment Date */}
        <View style={styles.row}>
          <TextInput
            style={[styles.input, {flex: 1}]}
            placeholder="Enter Amount*"
            keyboardType="numeric"
            value={amount}
            onChangeText={setAmount}
          />
          <TouchableOpacity
            style={[styles.input, {flex: 1}]}
            onPress={() => setShowDatePicker(true)}>
            <Text style={{color: repaymentDate ? '#000' : '#999'}}>
              {repaymentDate ? repaymentDate.toDateString() : 'Repayment date*'}
            </Text>
          </TouchableOpacity>
        </View>
        {showDatePicker && (
          <DateTimePicker
            value={repaymentDate}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(false);
              if (selectedDate) setRepaymentDate(selectedDate);
            }}
          />
        )}

        {/* Interest & Switch */}
        <View style={styles.row}>
          <TextInput
            style={[styles.input, {flex: 1}]}
            placeholder="Enter Interest (Optional)"
            keyboardType="numeric"
            value={interest}
            onChangeText={setInterest}
          />
          <View style={styles.switchRow}>
            <Text style={{marginRight: 8}}>Borrow as anonymous</Text>
            <Switch value={isAnonymous} onValueChange={setIsAnonymous} />
          </View>
        </View>

        {/* Validity */}
        <View style={styles.row}>
          <TextInput
            style={[styles.input, {flex: 1}]}
            placeholder="Enter validity (Optional)"
            keyboardType="numeric"
            value={validity}
            onChangeText={setValidity}
          />
          <Dropdown
            style={[styles.dropdown, {flex: 1}]}
            data={units}
            labelField="label"
            valueField="value"
            value={validityUnit}
            onChange={item => setValidityUnit(item.value)}
          />
        </View>

        <Text style={styles.note}>
          Note: Validity refers to the timeframe within which you require loan.
        </Text>

        {/* Custom Message */}
        <TextInput
          style={[styles.input, styles.textArea]}
          placeholder="Add Custom message"
          multiline
          numberOfLines={4}
          value={customMessage}
          onChangeText={setCustomMessage}
        />

        {/* Borrow Button */}
        <TouchableOpacity style={styles.borrowBtn}>
          <Text style={{color: '#fff', fontSize: 16}}>Borrow</Text>
        </TouchableOpacity>
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#fff'},
  dropdown: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
    height: 50,
    justifyContent: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
    height: 50,
    justifyContent: 'center',
  },
  row: {flexDirection: 'row', gap: 8, alignItems: 'center'},
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 50,
    marginBottom: 12,
    maxWidth: 150,
  },
  note: {fontSize: 12, color: '#777', marginBottom: 12},
  textArea: {height: 100, textAlignVertical: 'top'},
  borrowBtn: {
    backgroundColor: '#999',
    borderRadius: 25,
    alignItems: 'center',
    paddingVertical: 14,
    marginTop: 10,
  },
});
