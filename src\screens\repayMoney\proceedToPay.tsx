import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import {ArrowLeft, Calendar, Clock, ThumbsUp} from 'phosphor-react-native'; // ✅ phosphor-react-native
import {useNavigation} from '@react-navigation/native';
import Layout from '../../components/Layout';

export default function ProceedToPayScreen() {
  const navigation = useNavigation();

  return (
    <Layout
      title="Repay money"
      showBack
      style={styles.container}
      // rightContent={renderRightContent()}
    >
      <View>
        {/* Scrollable Content */}
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* User Info */}
          <View style={styles.userSection}>
            <Image
              source={{uri: 'https://via.placeholder.com/60'}}
              style={styles.avatar}
            />
            <View>
              <Text style={styles.userName}>R<PERSON><PERSON></Text>
              <View style={styles.tag}>
                <Text style={styles.tagText}>Close Friends</Text>
              </View>
            </View>
          </View>

          {/* Repayment Info */}
          <View style={styles.row}>
            <Calendar
              size={20}
              color="#555"
              weight="regular"
              style={styles.icon}
            />
            <Text style={styles.label}>Repayment date</Text>
            <Text style={styles.value}>Jan 2, 2025</Text>
          </View>

          <View style={styles.row}>
            <Clock
              size={20}
              color="#555"
              weight="regular"
              style={styles.icon}
            />
            <Text style={styles.label}>Loan Duration</Text>
            <Text style={styles.value}>4 months</Text>
          </View>

          {/* Amount Card */}
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Amount to be repaid</Text>

            <View style={styles.amountRow}>
              <Text style={styles.label}>Amount</Text>
              <Text style={styles.value}>Rs 999</Text>
            </View>

            <View style={styles.amountRow}>
              <Text style={styles.label}>Interest (5%)</Text>
              <Text style={styles.value}>Rs 45</Text>
            </View>

            <View style={styles.divider} />

            <View style={styles.amountRow}>
              <Text style={[styles.label, {fontWeight: 'bold'}]}>Total</Text>
              <Text style={[styles.value, {fontWeight: 'bold'}]}>Rs 1045</Text>
            </View>
          </View>

          {/* Anonymous Section */}
          <View style={styles.anonymous}>
            <ThumbsUp size={22} color="#0d47a1" weight="regular" />
            <View style={{marginLeft: 8}}>
              <Text style={styles.anonymousTitle}>Anonymous</Text>
              <Text style={styles.anonymousText}>
                You are anonymous to your friend.
              </Text>
            </View>
          </View>

          {/* Thank You Note */}
          <TextInput
            style={styles.input}
            placeholder="Add a thank you note..."
            placeholderTextColor="#888"
          />
        </ScrollView>

        {/* Fixed Bottom Button */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('PaymentOptions')}>
            <Text style={styles.buttonText}>Proceed to pay</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: '#fff'},
  scrollContent: {padding: 16, paddingBottom: 80},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  headerTitle: {fontSize: 18, fontWeight: '600', marginLeft: 10},
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatar: {width: 50, height: 50, borderRadius: 25, marginRight: 12},
  userName: {fontSize: 16, fontWeight: '600'},
  tag: {
    backgroundColor: '#e6f4ea',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    marginTop: 4,
    alignSelf: 'flex-start',
  },
  tagText: {fontSize: 12, color: '#2e7d32'},
  row: {flexDirection: 'row', alignItems: 'center', marginBottom: 12},
  icon: {marginRight: 8},
  label: {flex: 1, fontSize: 14, color: '#555'},
  value: {fontSize: 14, fontWeight: '500'},
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginVertical: 20,
  },
  cardTitle: {fontSize: 15, fontWeight: '600', marginBottom: 12},
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 6,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    marginVertical: 10,
  },
  anonymous: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f9ff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  anonymousTitle: {fontWeight: '600', fontSize: 14, color: '#0d47a1'},
  anonymousText: {fontSize: 12, color: '#555'},
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginBottom: 20,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#0d47a1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {color: '#fff', fontSize: 16, fontWeight: '600'},
});
