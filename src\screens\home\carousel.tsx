import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Animated,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import RepaymentCard from './RepaymentCard';

const {width} = Dimensions.get('window');

const CARD_WIDTH = 361; // Fixed width from design
const CARD_HEIGHT = 190; // Fixed height from design
const SPACING = 12;
const SIDE_SPACING = (width - CARD_WIDTH) / 2;

const data = [
  {
    id: '1',
    title: 'Repayment day',
    text: 'Gentle Reminder: You have to Repay to Saksham Rs. 15,000 today.',
  },
  {
    id: '2',
    title: 'Upcoming Repayment',
    text: 'Heads-up: Repayment to Rohit of Rs. 10,000 is due tomorrow.',
  },
  {
    id: '3',
    title: 'Payment Due Soon',
    text: 'Reminder: You owe Priya Rs. 5,000 in 3 days.',
  },
];

// Duplicate data for smooth infinite scrolling
const loopedData = [...data, ...data, ...data];

const RepaymentCarousel = () => {
  const navigation = useNavigation();
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(data.length);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [isScrolling, setIsScrolling] = useState(false);

  const scrollToIndex = (index: number, animated = true) => {
    flatListRef.current?.scrollToIndex({
      index,
      animated,
    });
  };

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));

    if (currentIndex >= data.length * 2) {
      scrollToIndex(currentIndex - data.length, false);
    } else if (currentIndex <= data.length - 1) {
      scrollToIndex(currentIndex + data.length, false);
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;

    const startAutoScroll = () => {
      interval = setInterval(() => {
        if (!isScrolling) {
          const nextIndex = (currentIndex % data.length) + 1 + data.length;
          scrollToIndex(nextIndex);
        }
      }, 3000);
    };

    startAutoScroll();

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentIndex, isScrolling]);

  const onMomentumScrollEnd = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffset / (CARD_WIDTH + SPACING));
    setCurrentIndex(newIndex % data.length);
    setIsScrolling(false);
  };

  const handleLater = () => {
    console.log('Later clicked');
  };

  const handleViewMore = () => {
    navigation.navigate('RepayMoney' as never);
  };

  return (
    <View style={styles.container}>
      <Animated.FlatList
        ref={flatListRef}
        data={loopedData}
        keyExtractor={(item, index) => `${item.id}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{paddingHorizontal: SIDE_SPACING}}
        snapToInterval={CARD_WIDTH + SPACING}
        decelerationRate="fast"
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {x: scrollX}}}],
          {
            useNativeDriver: true,
            listener: handleScroll,
          },
        )}
        scrollEventThrottle={16}
        onMomentumScrollEnd={onMomentumScrollEnd}
        onScrollBeginDrag={() => setIsScrolling(true)}
        initialScrollIndex={data.length}
        getItemLayout={(_, index) => ({
          length: CARD_WIDTH + SPACING,
          offset: (CARD_WIDTH + SPACING) * index,
          index,
        })}
        renderItem={({item, index}) => {
          const inputRange = [
            (index - 2) * (CARD_WIDTH + SPACING),
            (index - 1) * (CARD_WIDTH + SPACING),
            index * (CARD_WIDTH + SPACING),
          ];

          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [1, 1, 1],
            extrapolate: 'clamp',
          });

          const opacity = scrollX.interpolate({
            inputRange,
            // outputRange: [0.6, 1, 0.6],
            outputRange: [1, 1, 1],
            extrapolate: 'clamp',
          });

          return (
            <Animated.View
              style={[
                styles.cardContainer,
                {
                  width: CARD_WIDTH,
                  height: CARD_HEIGHT,
                  marginHorizontal: SPACING / 2,
                  transform: [{scale}],
                  opacity,
                },
              ]}>
              <RepaymentCard
                title={item.title}
                text={item.text}
                onLater={handleLater}
                onViewMore={handleViewMore}
              />
            </Animated.View>
          );
        }}
      />

      {/* Indicators */}
      <View style={styles.indicatorContainer}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              currentIndex % data.length === index && styles.activeDot,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: CARD_HEIGHT + 20,
    marginTop: 8,
  },
  cardContainer: {
    borderRadius: 16,
    backgroundColor: 'transparent',
    overflow: 'hidden',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  dot: {
    width: 24,
    height: 5,
    borderRadius: 4,
    backgroundColor: '#A5A5A5',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#003272',
    width: 24,
    borderRadius: 4,
  },
});

export default RepaymentCarousel;
